#!/usr/bin/env python3
"""
AutoGen编程工作流启动脚本
提供交互式界面来运行不同的工作流
"""

import asyncio
import os
import sys
from typing import Dict, Any

from programming_workflow import ProgrammingWorkflow
from advanced_programming_workflow import AdvancedProgrammingWorkflow
from config import WorkflowConfig, TASK_TEMPLATES


class WorkflowRunner:
    """工作流运行器"""
    
    def __init__(self):
        self.basic_workflow = None
        self.advanced_workflow = None
    
    async def initialize(self):
        """初始化工作流"""
        print("🔧 初始化工作流...")
        
        # 验证配置
        if not WorkflowConfig.validate_config():
            print("❌ 配置验证失败！")
            print("💡 请设置 OPENAI_API_KEY 环境变量")
            return False
        
        try:
            self.basic_workflow = ProgrammingWorkflow()
            self.advanced_workflow = AdvancedProgrammingWorkflow()
            print("✅ 工作流初始化完成")
            return True
        except Exception as e:
            print(f"❌ 初始化失败: {str(e)}")
            return False
    
    async def cleanup(self):
        """清理资源"""
        if self.basic_workflow:
            await self.basic_workflow.close()
        if self.advanced_workflow:
            await self.advanced_workflow.close()
    
    def show_menu(self):
        """显示主菜单"""
        print("\n" + "=" * 60)
        print("🤖 AutoGen编程工作流")
        print("=" * 60)
        print("1. 基础工作流 (CodeWriter → CodeReviewer → CodeOptimizer)")
        print("2. 高级工作流 (智能Agent选择 + 工具集成)")
        print("3. 使用预定义模板")
        print("4. 自定义任务")
        print("5. 查看配置信息")
        print("0. 退出")
        print("=" * 60)
    
    def show_template_menu(self):
        """显示模板菜单"""
        print("\n📋 预定义任务模板:")
        print("-" * 40)
        templates = list(TASK_TEMPLATES.keys())
        for i, template in enumerate(templates, 1):
            print(f"{i}. {template.replace('_', ' ').title()}")
        print("0. 返回主菜单")
        return templates
    
    async def run_basic_workflow(self):
        """运行基础工作流"""
        print("\n🔥 基础编程工作流")
        print("-" * 40)
        
        task = input("请输入编程任务描述: ").strip()
        if not task:
            print("❌ 任务描述不能为空")
            return
        
        print(f"\n🚀 开始执行任务: {task}")
        try:
            await self.basic_workflow.run_programming_task_stream(task)
            print("\n✅ 任务执行完成！")
        except Exception as e:
            print(f"\n❌ 执行失败: {str(e)}")
    
    async def run_advanced_workflow(self):
        """运行高级工作流"""
        print("\n🚀 高级编程工作流")
        print("-" * 40)
        
        task = input("请输入编程任务描述: ").strip()
        if not task:
            print("❌ 任务描述不能为空")
            return
        
        project_type = input("请输入项目类型 (可选): ").strip() or "general"
        
        print(f"\n🚀 开始执行任务: {task}")
        print(f"📋 项目类型: {project_type}")
        
        try:
            await self.advanced_workflow.run_advanced_task(task, project_type)
            print("\n✅ 任务执行完成！")
        except Exception as e:
            print(f"\n❌ 执行失败: {str(e)}")
    
    async def run_template_task(self):
        """运行模板任务"""
        templates = self.show_template_menu()
        
        try:
            choice = int(input("\n请选择模板 (输入数字): "))
            if choice == 0:
                return
            if 1 <= choice <= len(templates):
                template_key = templates[choice - 1]
                template_task = TASK_TEMPLATES[template_key]
                
                print(f"\n📋 选择的模板: {template_key.replace('_', ' ').title()}")
                print(f"📝 模板内容:\n{template_task}")
                
                # 询问是否要添加自定义需求
                custom = input("\n是否要添加自定义需求? (y/N): ").strip().lower()
                if custom == 'y':
                    additional = input("请输入额外需求: ").strip()
                    if additional:
                        template_task += f"\n\n额外需求:\n{additional}"
                
                print(f"\n🚀 开始执行模板任务...")
                await self.advanced_workflow.run_advanced_task(template_task, template_key)
                print("\n✅ 模板任务执行完成！")
            else:
                print("❌ 无效选择")
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 执行失败: {str(e)}")
    
    async def run_custom_task(self):
        """运行自定义任务"""
        print("\n🎯 自定义任务")
        print("-" * 40)
        print("请输入详细的任务描述（支持多行，输入空行结束）:")
        
        lines = []
        while True:
            line = input()
            if not line.strip():
                break
            lines.append(line)
        
        if not lines:
            print("❌ 任务描述不能为空")
            return
        
        task = "\n".join(lines)
        
        # 选择工作流类型
        print("\n选择工作流类型:")
        print("1. 基础工作流")
        print("2. 高级工作流")
        
        try:
            workflow_choice = int(input("请选择 (1-2): "))
            
            print(f"\n🚀 开始执行自定义任务...")
            
            if workflow_choice == 1:
                await self.basic_workflow.run_programming_task_stream(task)
            elif workflow_choice == 2:
                project_type = input("请输入项目类型 (可选): ").strip() or "custom"
                await self.advanced_workflow.run_advanced_task(task, project_type)
            else:
                print("❌ 无效选择")
                return
            
            print("\n✅ 自定义任务执行完成！")
            
        except ValueError:
            print("❌ 请输入有效数字")
        except Exception as e:
            print(f"❌ 执行失败: {str(e)}")
    
    def show_config_info(self):
        """显示配置信息"""
        print("\n⚙️  配置信息")
        print("-" * 40)
        
        config = WorkflowConfig.get_model_config()
        selector_config = WorkflowConfig.get_selector_config()
        
        print(f"主模型: {config['model']}")
        print(f"选择器模型: {selector_config['model']}")
        print(f"最大消息数: {WorkflowConfig.MAX_MESSAGES}")
        print(f"输出目录: {WorkflowConfig.TOOLS_CONFIG['output_directory']}")
        
        # 检查API密钥
        api_key = WorkflowConfig.OPENAI_API_KEY
        if api_key:
            print(f"API密钥: {'*' * 20}{api_key[-4:]}")
        else:
            print("API密钥: ❌ 未设置")
        
        print(f"\n终止关键词: {', '.join(WorkflowConfig.TERMINATION_KEYWORDS)}")
    
    async def run(self):
        """运行主程序"""
        print("🎉 欢迎使用AutoGen编程工作流！")
        
        # 初始化
        if not await self.initialize():
            return
        
        # 创建输出目录
        os.makedirs(WorkflowConfig.TOOLS_CONFIG["output_directory"], exist_ok=True)
        
        try:
            while True:
                self.show_menu()
                
                try:
                    choice = input("\n请选择操作 (输入数字): ").strip()
                    
                    if choice == "0":
                        print("👋 再见！")
                        break
                    elif choice == "1":
                        await self.run_basic_workflow()
                    elif choice == "2":
                        await self.run_advanced_workflow()
                    elif choice == "3":
                        await self.run_template_task()
                    elif choice == "4":
                        await self.run_custom_task()
                    elif choice == "5":
                        self.show_config_info()
                    else:
                        print("❌ 无效选择，请重新输入")
                
                except KeyboardInterrupt:
                    print("\n\n👋 用户中断，退出程序")
                    break
                except Exception as e:
                    print(f"\n❌ 发生错误: {str(e)}")
                    print("💡 请检查网络连接和API配置")
        
        finally:
            await self.cleanup()


async def main():
    """主函数"""
    # 设置事件循环策略（Windows兼容性）
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    runner = WorkflowRunner()
    await runner.run()


if __name__ == "__main__":
    asyncio.run(main())
