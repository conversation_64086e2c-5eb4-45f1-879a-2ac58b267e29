"""
AutoGen编程工作流配置文件
"""

import os
from typing import Dict, Any

class WorkflowConfig:
    """工作流配置类"""
    
    # OpenAI配置
    OPENAI_API_KEY = os.getenv("OPENAI_API_KEY")
    DEFAULT_MODEL = "gpt-4o"
    SELECTOR_MODEL = "gpt-4o-mini"
    
    # 工作流配置
    MAX_MESSAGES = 25
    MAX_TURNS_PER_AGENT = 3
    
    # Agent系统消息模板
    AGENT_SYSTEM_MESSAGES = {
        "code_writer": """你是一个专业的代码编写专家。你的职责是：
1. 根据用户需求编写高质量的代码
2. 确保代码具有良好的可读性和结构
3. 添加必要的注释和文档
4. 遵循最佳编程实践和代码规范
5. 考虑代码的性能和安全性

请在代码编写完成后，简要说明你的设计思路和实现要点。
编写完代码后，请说 "CODE_WRITTEN" 表示代码编写完成。""",
        
        "code_reviewer": """你是一个经验丰富的代码审查专家。你的职责是：
1. 仔细审查提供的代码
2. 检查代码的正确性、可读性和性能
3. 识别潜在的bug、安全漏洞和改进点
4. 提出具体的修改建议和最佳实践建议
5. 评估代码是否符合编程规范和设计模式

请提供详细的审查报告，包括：
- 代码优点
- 发现的问题
- 具体的改进建议
- 推荐的最佳实践

审查完成后，请说 "REVIEW_COMPLETED" 表示审查完成。""",
        
        "code_optimizer": """你是一个代码优化专家。你的职责是：
1. 分析原始代码和审查建议
2. 综合考虑所有反馈和建议
3. 重新编写和优化代码
4. 确保优化后的代码解决了审查中发现的问题
5. 提升代码的性能、可维护性和可扩展性

请提供：
- 优化后的完整代码
- 优化说明和改进点总结
- 与原始代码的对比分析

优化完成后，请说 "OPTIMIZATION_COMPLETED" 表示优化完成。"""
    }
    
    # 终止关键词
    TERMINATION_KEYWORDS = [
        "CODE_WRITTEN",
        "REVIEW_COMPLETED", 
        "OPTIMIZATION_COMPLETED",
        "PROJECT_COMPLETED",
        "ARCHITECTURE_DESIGN_COMPLETED"
    ]
    
    # 工具配置
    TOOLS_CONFIG = {
        "enable_file_operations": True,
        "enable_code_analysis": True,
        "enable_testing": True,
        "output_directory": "./output"
    }
    
    @classmethod
    def get_model_config(cls) -> Dict[str, Any]:
        """获取模型配置"""
        return {
            "model": cls.DEFAULT_MODEL,
            "api_key": cls.OPENAI_API_KEY,
            "temperature": 0.7,
            "max_tokens": 4000
        }
    
    @classmethod
    def get_selector_config(cls) -> Dict[str, Any]:
        """获取选择器模型配置"""
        return {
            "model": cls.SELECTOR_MODEL,
            "api_key": cls.OPENAI_API_KEY,
            "temperature": 0.3,
            "max_tokens": 1000
        }
    
    @classmethod
    def validate_config(cls) -> bool:
        """验证配置"""
        if not cls.OPENAI_API_KEY:
            print("警告: 未设置OPENAI_API_KEY环境变量")
            return False
        return True


# 预定义任务模板
TASK_TEMPLATES = {
    "web_application": """
    开发一个Web应用程序，要求：
    1. 使用现代Web框架（如Flask/Django/FastAPI）
    2. 包含用户认证和授权
    3. 提供RESTful API接口
    4. 包含数据库操作
    5. 前端界面友好
    6. 包含错误处理和日志记录
    """,
    
    "data_analysis": """
    开发一个数据分析工具，要求：
    1. 支持多种数据格式（CSV, JSON, Excel）
    2. 提供数据清洗和预处理功能
    3. 包含统计分析和可视化
    4. 支持机器学习模型
    5. 生成分析报告
    6. 性能优化，支持大数据集
    """,
    
    "api_service": """
    开发一个API服务，要求：
    1. 设计RESTful API架构
    2. 包含认证和权限控制
    3. 支持数据验证和序列化
    4. 包含缓存和限流机制
    5. 提供API文档
    6. 包含监控和日志
    """,
    
    "automation_tool": """
    开发一个自动化工具，要求：
    1. 支持任务调度和执行
    2. 提供配置文件管理
    3. 包含错误处理和重试机制
    4. 支持并发执行
    5. 提供进度监控
    6. 生成执行报告
    """
}


# 代码质量检查规则
CODE_QUALITY_RULES = {
    "naming_conventions": [
        "使用有意义的变量和函数名",
        "遵循PEP 8命名规范",
        "避免使用缩写和单字母变量名"
    ],
    
    "code_structure": [
        "函数长度不超过50行",
        "类的方法数量不超过20个",
        "避免深层嵌套（不超过4层）",
        "使用适当的设计模式"
    ],
    
    "documentation": [
        "所有公共函数都有文档字符串",
        "复杂逻辑添加注释说明",
        "包含使用示例",
        "API文档完整"
    ],
    
    "error_handling": [
        "使用适当的异常处理",
        "避免裸露的except语句",
        "提供有意义的错误消息",
        "记录错误日志"
    ],
    
    "performance": [
        "避免不必要的循环",
        "使用适当的数据结构",
        "考虑内存使用效率",
        "优化数据库查询"
    ],
    
    "security": [
        "验证所有输入数据",
        "避免SQL注入和XSS攻击",
        "使用安全的密码存储",
        "保护敏感信息"
    ]
}
