"""
高级AutoGen编程工作流
使用SelectorGroupChat实现更智能的agent选择和协作
包含工具使用、状态管理和更复杂的工作流控制
"""

import asyncio
import json
import os
from typing import List, Sequence, Dict, Any
from dataclasses import dataclass

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import Selector<PERSON>roupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.ui import Console
from autogen_core.tools import FunctionTool
from autogen_ext.models.openai import OpenAIChatCompletionClient


@dataclass
class CodeAnalysisResult:
    """代码分析结果"""
    complexity_score: int
    maintainability_score: int
    security_issues: List[str]
    performance_issues: List[str]
    suggestions: List[str]


class AdvancedProgrammingWorkflow:
    """高级编程工作流管理类"""
    
    def __init__(self, model_name: str = "gpt-4o", api_key: str = None):
        """初始化高级编程工作流"""
        # 创建模型客户端
        if api_key:
            self.model_client = OpenAIChatCompletionClient(
                model=model_name,
                api_key=api_key
            )
        else:
            self.model_client = OpenAIChatCompletionClient(model=model_name)
        
        # 创建选择器模型客户端（使用更小的模型来节省成本）
        self.selector_client = OpenAIChatCompletionClient(model="gpt-4o-mini")
        
        # 初始化工具
        self._setup_tools()
        
        # 初始化agents
        self._setup_agents()
        
        # 设置终止条件
        self._setup_termination_conditions()
        
        # 创建智能团队
        self._setup_intelligent_team()
    
    def _setup_tools(self):
        """设置工具函数"""
        
        def save_code_to_file(filename: str, code: str) -> str:
            """保存代码到文件"""
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(code)
                return f"代码已成功保存到 {filename}"
            except Exception as e:
                return f"保存失败: {str(e)}"
        
        def analyze_code_complexity(code: str) -> str:
            """分析代码复杂度（简化版本）"""
            lines = code.split('\n')
            non_empty_lines = [line for line in lines if line.strip()]
            
            # 简单的复杂度计算
            complexity_indicators = [
                'if ', 'elif ', 'else:', 'for ', 'while ', 'try:', 'except:', 'with '
            ]
            
            complexity_score = 0
            for line in non_empty_lines:
                for indicator in complexity_indicators:
                    if indicator in line:
                        complexity_score += 1
            
            # 计算维护性评分
            maintainability_score = max(0, 100 - complexity_score * 2)
            
            result = {
                "total_lines": len(lines),
                "code_lines": len(non_empty_lines),
                "complexity_score": complexity_score,
                "maintainability_score": maintainability_score,
                "analysis": f"代码复杂度: {complexity_score}, 维护性评分: {maintainability_score}/100"
            }
            
            return json.dumps(result, ensure_ascii=False, indent=2)
        
        def run_code_tests(code: str) -> str:
            """运行代码测试（模拟）"""
            # 这里是一个简化的测试模拟
            test_results = {
                "syntax_check": "通过",
                "basic_functionality": "通过", 
                "edge_cases": "需要改进",
                "performance": "良好",
                "security": "需要审查"
            }
            
            return json.dumps(test_results, ensure_ascii=False, indent=2)
        
        # 创建工具
        self.save_code_tool = FunctionTool(
            save_code_to_file,
            description="将代码保存到指定文件"
        )
        
        self.analyze_complexity_tool = FunctionTool(
            analyze_code_complexity,
            description="分析代码复杂度和维护性"
        )
        
        self.run_tests_tool = FunctionTool(
            run_code_tests,
            description="运行代码测试和质量检查"
        )
    
    def _setup_agents(self):
        """设置专业agents"""
        
        # 项目经理Agent - 负责协调和决策
        self.project_manager = AssistantAgent(
            name="ProjectManager",
            description="项目经理，负责协调团队工作和做出决策",
            model_client=self.model_client,
            system_message="""你是一个经验丰富的项目经理。你的职责是：

1. 分析用户需求并制定开发计划
2. 协调团队成员的工作
3. 确保项目按时高质量完成
4. 做出关键决策和优先级排序
5. 监控项目进度和质量

当你认为任务完成时，请说 "PROJECT_COMPLETED"。
当需要特定专家介入时，请明确指出需要哪个专家。"""
        )
        
        # 高级代码编写者
        self.senior_developer = AssistantAgent(
            name="SeniorDeveloper", 
            description="高级开发者，负责编写高质量代码",
            model_client=self.model_client,
            tools=[self.save_code_tool],
            system_message="""你是一个资深的软件开发专家。你的职责是：

1. 编写高质量、可维护的代码
2. 遵循最佳实践和设计模式
3. 考虑性能、安全性和可扩展性
4. 编写清晰的文档和注释
5. 使用工具保存代码到文件

编写完代码后，请说 "CODE_DEVELOPMENT_COMPLETED"。"""
        )
        
        # 代码审查专家
        self.code_reviewer = AssistantAgent(
            name="CodeReviewer",
            description="代码审查专家，负责代码质量检查",
            model_client=self.model_client,
            tools=[self.analyze_complexity_tool, self.run_tests_tool],
            system_message="""你是一个代码审查专家。你的职责是：

1. 进行全面的代码审查
2. 使用工具分析代码复杂度
3. 运行测试检查代码质量
4. 识别潜在问题和改进点
5. 提供详细的审查报告

审查完成后，请说 "CODE_REVIEW_COMPLETED"。"""
        )
        
        # 架构师
        self.architect = AssistantAgent(
            name="Architect",
            description="软件架构师，负责系统设计和架构决策",
            model_client=self.model_client,
            system_message="""你是一个软件架构师。你的职责是：

1. 设计系统架构和模块结构
2. 制定技术选型和设计决策
3. 确保系统的可扩展性和可维护性
4. 提供架构指导和最佳实践建议
5. 解决复杂的技术问题

架构设计完成后，请说 "ARCHITECTURE_DESIGN_COMPLETED"。"""
        )
        
        # 优化专家
        self.optimizer = AssistantAgent(
            name="Optimizer",
            description="性能优化专家，负责代码和系统优化",
            model_client=self.model_client,
            tools=[self.save_code_tool, self.analyze_complexity_tool],
            system_message="""你是一个性能优化专家。你的职责是：

1. 分析代码性能瓶颈
2. 优化算法和数据结构
3. 改进代码效率和资源使用
4. 确保优化后的代码质量
5. 使用工具保存优化后的代码

优化完成后，请说 "OPTIMIZATION_COMPLETED"。"""
        )
    
    def _setup_termination_conditions(self):
        """设置终止条件"""
        text_termination = TextMentionTermination("PROJECT_COMPLETED")
        max_messages_termination = MaxMessageTermination(max_messages=25)
        self.termination_condition = text_termination | max_messages_termination
    
    def _setup_intelligent_team(self):
        """设置智能团队，使用SelectorGroupChat进行动态选择"""
        
        def selector_func(messages: Sequence) -> str | None:
            """自定义选择器函数"""
            if not messages:
                return "ProjectManager"  # 项目开始时由项目经理主导
            
            last_message = messages[-1]
            
            # 根据最后一条消息的来源决定下一个发言者
            if hasattr(last_message, 'source'):
                if last_message.source == "ProjectManager":
                    # 项目经理发言后，通常需要开发者或架构师
                    if "架构" in str(last_message.content) or "设计" in str(last_message.content):
                        return "Architect"
                    else:
                        return "SeniorDeveloper"
                elif last_message.source == "SeniorDeveloper":
                    return "CodeReviewer"  # 开发完成后进行审查
                elif last_message.source == "CodeReviewer":
                    return "Optimizer"     # 审查后进行优化
                elif last_message.source == "Optimizer":
                    return "ProjectManager"  # 优化后回到项目经理
                elif last_message.source == "Architect":
                    return "SeniorDeveloper"  # 架构设计后开始开发
            
            return None  # 使用LLM选择
        
        self.team = SelectorGroupChat(
            participants=[
                self.project_manager,
                self.senior_developer,
                self.code_reviewer,
                self.architect,
                self.optimizer
            ],
            model_client=self.selector_client,
            termination_condition=self.termination_condition,
            selector_func=selector_func,
            allow_repeated_speaker=True
        )
    
    async def run_advanced_task(self, task_description: str, project_type: str = "general"):
        """运行高级编程任务"""
        print(f"🚀 开始执行高级编程任务")
        print(f"📋 任务类型: {project_type}")
        print(f"📝 任务描述: {task_description}")
        print("=" * 80)
        
        enhanced_task = f"""
项目类型: {project_type}
任务描述: {task_description}

团队成员及职责：
- ProjectManager: 项目协调和决策
- SeniorDeveloper: 代码开发和实现
- CodeReviewer: 代码审查和质量检查
- Architect: 系统架构设计
- Optimizer: 性能优化

请ProjectManager开始分析需求并制定开发计划。
"""
        
        try:
            await Console(self.team.run_stream(task=enhanced_task))
            print("\n" + "=" * 80)
            print("✅ 高级编程工作流执行完成!")
            
        except Exception as e:
            print(f"❌ 执行过程中出现错误: {str(e)}")
            raise
    
    async def save_team_state(self) -> Dict[str, Any]:
        """保存团队状态"""
        return await self.team.save_state()
    
    async def load_team_state(self, state: Dict[str, Any]):
        """加载团队状态"""
        await self.team.load_state(state)
    
    async def close(self):
        """关闭资源"""
        await self.model_client.close()
        await self.selector_client.close()


async def main():
    """主函数示例"""
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        # 高级任务示例
        task = """
        设计并实现一个分布式任务调度系统，要求：
        
        1. 核心功能：
           - 支持任务的创建、调度和执行
           - 支持任务优先级和依赖关系
           - 提供任务状态监控和日志记录
           
        2. 技术要求：
           - 使用Python实现
           - 支持多进程/多线程执行
           - 具备容错和重试机制
           - 提供REST API接口
           
        3. 质量要求：
           - 代码结构清晰，易于维护
           - 包含完整的错误处理
           - 性能优化，支持高并发
           - 包含单元测试
        """
        
        await workflow.run_advanced_task(task, "distributed_system")
        
    finally:
        await workflow.close()


if __name__ == "__main__":
    asyncio.run(main())
