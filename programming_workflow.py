"""
AutoGen编程工作流
实现三个agent的协作：
1. CodeWriterAgent - 负责编写代码
2. CodeReviewerAgent - 负责审查代码并提出修改建议  
3. CodeOptimizerAgent - 负责根据代码和建议进行优化

基于AutoGen最新版本的API和最佳实践
"""

import asyncio
from typing import List, Sequence
from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.teams import RoundRobinGroupChat
from autogen_agentchat.conditions import TextMentionTermination, MaxMessageTermination
from autogen_agentchat.messages import TextMessage
from autogen_agentchat.ui import Console
from autogen_ext.models.openai import OpenAIChatCompletionClient


class ProgrammingWorkflow:
    """编程工作流管理类"""
    
    def __init__(self, model_name: str = "gpt-4o", api_key: str = None):
        """
        初始化编程工作流
        
        Args:
            model_name: 使用的模型名称
            api_key: OpenAI API密钥，如果为None则从环境变量获取
        """
        # 创建模型客户端
        if api_key:
            self.model_client = OpenAIChatCompletionClient(
                model=model_name,
                api_key=api_key
            )
        else:
            self.model_client = OpenAIChatCompletionClient(model=model_name)
        
        # 初始化agents
        self._setup_agents()
        
        # 设置终止条件
        self._setup_termination_conditions()
        
        # 创建团队
        self._setup_team()
    
    def _setup_agents(self):
        """设置三个专业agent"""
        
        # Agent 1: 代码编写者
        self.code_writer = AssistantAgent(
            name="CodeWriter",
            model_client=self.model_client,
            system_message="""你是一个专业的代码编写专家。你的职责是：

1. 根据用户需求编写高质量的代码
2. 确保代码具有良好的可读性和结构
3. 添加必要的注释和文档
4. 遵循最佳编程实践和代码规范
5. 考虑代码的性能和安全性

请在代码编写完成后，简要说明你的设计思路和实现要点。
编写完代码后，请说 "CODE_WRITTEN" 表示代码编写完成。"""
        )
        
        # Agent 2: 代码审查者
        self.code_reviewer = AssistantAgent(
            name="CodeReviewer", 
            model_client=self.model_client,
            system_message="""你是一个经验丰富的代码审查专家。你的职责是：

1. 仔细审查提供的代码
2. 检查代码的正确性、可读性和性能
3. 识别潜在的bug、安全漏洞和改进点
4. 提出具体的修改建议和最佳实践建议
5. 评估代码是否符合编程规范和设计模式

请提供详细的审查报告，包括：
- 代码优点
- 发现的问题
- 具体的改进建议
- 推荐的最佳实践

审查完成后，请说 "REVIEW_COMPLETED" 表示审查完成。"""
        )
        
        # Agent 3: 代码优化者
        self.code_optimizer = AssistantAgent(
            name="CodeOptimizer",
            model_client=self.model_client,
            system_message="""你是一个代码优化专家。你的职责是：

1. 分析原始代码和审查建议
2. 综合考虑所有反馈和建议
3. 重新编写和优化代码
4. 确保优化后的代码解决了审查中发现的问题
5. 提升代码的性能、可维护性和可扩展性

请提供：
- 优化后的完整代码
- 优化说明和改进点总结
- 与原始代码的对比分析

优化完成后，请说 "OPTIMIZATION_COMPLETED" 表示优化完成。"""
        )
    
    def _setup_termination_conditions(self):
        """设置终止条件"""
        # 当优化完成时终止
        text_termination = TextMentionTermination("OPTIMIZATION_COMPLETED")
        # 设置最大消息数量防止无限循环
        max_messages_termination = MaxMessageTermination(max_messages=15)
        # 组合终止条件
        self.termination_condition = text_termination | max_messages_termination
    
    def _setup_team(self):
        """设置团队，按照固定顺序进行协作"""
        self.team = RoundRobinGroupChat(
            participants=[
                self.code_writer,      # 首先编写代码
                self.code_reviewer,    # 然后审查代码
                self.code_optimizer    # 最后优化代码
            ],
            termination_condition=self.termination_condition
        )
    
    async def run_programming_task(self, task_description: str):
        """
        运行编程任务
        
        Args:
            task_description: 编程任务描述
            
        Returns:
            任务执行结果
        """
        print(f"🚀 开始执行编程任务: {task_description}")
        print("=" * 80)
        
        # 构建详细的任务提示
        detailed_task = f"""
编程任务: {task_description}

工作流程：
1. CodeWriter将首先编写代码
2. CodeReviewer将审查代码并提出建议
3. CodeOptimizer将根据代码和建议进行优化

请开始执行任务。
"""
        
        try:
            # 运行团队协作
            result = await self.team.run(task=detailed_task)
            
            print("\n" + "=" * 80)
            print("✅ 编程工作流执行完成!")
            print(f"📊 总消息数: {len(result.messages)}")
            print(f"🛑 停止原因: {result.stop_reason}")
            
            return result
            
        except Exception as e:
            print(f"❌ 执行过程中出现错误: {str(e)}")
            raise
    
    async def run_programming_task_stream(self, task_description: str):
        """
        流式运行编程任务，实时显示进度
        
        Args:
            task_description: 编程任务描述
        """
        print(f"🚀 开始执行编程任务: {task_description}")
        print("=" * 80)
        
        detailed_task = f"""
编程任务: {task_description}

工作流程：
1. CodeWriter将首先编写代码
2. CodeReviewer将审查代码并提出建议  
3. CodeOptimizer将根据代码和建议进行优化

请开始执行任务。
"""
        
        try:
            # 使用Console进行流式输出
            await Console(self.team.run_stream(task=detailed_task))
            
            print("\n" + "=" * 80)
            print("✅ 编程工作流执行完成!")
            
        except Exception as e:
            print(f"❌ 执行过程中出现错误: {str(e)}")
            raise
    
    async def close(self):
        """关闭资源"""
        await self.model_client.close()


async def main():
    """主函数示例"""
    # 创建编程工作流实例
    workflow = ProgrammingWorkflow()
    
    try:
        # 示例任务1: 实现一个简单的计算器
        task1 = """
        请实现一个Python计算器类，要求：
        1. 支持基本的四则运算（加减乘除）
        2. 支持括号运算
        3. 具有历史记录功能
        4. 包含错误处理
        5. 提供清晰的用户接口
        """
        
        print("🔥 执行任务1: 计算器实现")
        await workflow.run_programming_task_stream(task1)
        
        print("\n" + "🔄" * 40)
        
        # 示例任务2: 数据处理工具
        task2 = """
        请实现一个数据处理工具，要求：
        1. 能够读取CSV文件
        2. 提供数据清洗功能（去重、处理缺失值）
        3. 支持基本的统计分析
        4. 能够导出处理后的数据
        5. 包含完整的错误处理和日志记录
        """
        
        print("🔥 执行任务2: 数据处理工具")
        await workflow.run_programming_task_stream(task2)
        
    finally:
        # 清理资源
        await workflow.close()


if __name__ == "__main__":
    # 运行主函数
    asyncio.run(main())
