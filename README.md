# AutoGen编程工作流

基于Microsoft AutoGen最新版本的多Agent协作编程工作流系统。实现了代码编写、审查和优化的完整流程，支持智能Agent选择和高级工作流控制。

## 🌟 特性

### 基础工作流 (ProgrammingWorkflow)
- **CodeWriterAgent**: 专业代码编写，遵循最佳实践
- **CodeReviewerAgent**: 全面代码审查，质量检查
- **CodeOptimizerAgent**: 性能优化，代码重构
- **RoundRobinGroupChat**: 固定顺序的Agent协作

### 高级工作流 (AdvancedProgrammingWorkflow)
- **ProjectManager**: 项目协调和决策管理
- **SeniorDeveloper**: 高级代码开发
- **CodeReviewer**: 代码质量检查（带工具）
- **Architect**: 系统架构设计
- **Optimizer**: 性能优化专家
- **SelectorGroupChat**: 智能Agent选择
- **工具集成**: 文件操作、代码分析、测试工具
- **状态管理**: 支持工作流状态保存和恢复

## 📦 安装

1. 克隆项目：
```bash
git clone <repository-url>
cd autogen-programming-workflow
```

2. 安装依赖：
```bash
pip install -r requirements.txt
```

3. 设置环境变量：
```bash
export OPENAI_API_KEY="your-openai-api-key"
```

## 🚀 快速开始

### 基础使用

```python
import asyncio
from programming_workflow import ProgrammingWorkflow

async def main():
    # 创建工作流
    workflow = ProgrammingWorkflow()
    
    # 定义编程任务
    task = """
    请实现一个Python计算器类，要求：
    1. 支持基本四则运算
    2. 支持括号运算
    3. 包含历史记录功能
    4. 提供错误处理
    """
    
    # 执行任务
    await workflow.run_programming_task_stream(task)
    
    # 清理资源
    await workflow.close()

asyncio.run(main())
```

### 高级使用

```python
import asyncio
from advanced_programming_workflow import AdvancedProgrammingWorkflow

async def main():
    # 创建高级工作流
    workflow = AdvancedProgrammingWorkflow()
    
    # 复杂项目任务
    task = """
    设计并实现一个分布式任务调度系统，要求：
    - 支持任务创建、调度和执行
    - 支持任务优先级和依赖关系
    - 提供REST API接口
    - 包含容错和重试机制
    """
    
    # 执行高级任务
    await workflow.run_advanced_task(task, "distributed_system")
    
    await workflow.close()

asyncio.run(main())
```

## 📁 项目结构

```
autogen-programming-workflow/
├── programming_workflow.py          # 基础工作流实现
├── advanced_programming_workflow.py # 高级工作流实现
├── config.py                       # 配置文件
├── example_usage.py                # 使用示例
├── requirements.txt                # 依赖包
├── README.md                       # 项目文档
└── output/                         # 生成的代码文件
```

## 🔧 配置说明

### 环境变量
- `OPENAI_API_KEY`: OpenAI API密钥（必需）

### 配置文件 (config.py)
- 模型配置：默认使用gpt-4o，选择器使用gpt-4o-mini
- Agent系统消息模板
- 终止条件关键词
- 代码质量检查规则
- 工具配置选项

## 🛠️ 工具功能

### 代码分析工具
- 复杂度分析
- 维护性评分
- 性能指标计算

### 文件操作工具
- 代码保存到文件
- 项目结构生成
- 批量文件处理

### 测试工具
- 语法检查
- 功能测试模拟
- 质量评估

## 📊 工作流程

### 基础工作流
1. **CodeWriter** 编写初始代码
2. **CodeReviewer** 审查代码并提出建议
3. **CodeOptimizer** 根据建议优化代码

### 高级工作流
1. **ProjectManager** 分析需求，制定计划
2. **Architect** 设计系统架构（如需要）
3. **SeniorDeveloper** 实现代码
4. **CodeReviewer** 质量检查和测试
5. **Optimizer** 性能优化
6. **ProjectManager** 项目总结

## 🎯 使用场景

### Web应用开发
- 博客系统
- 电商平台
- 管理系统
- API服务

### 数据分析
- 数据处理工具
- 机器学习项目
- 可视化系统
- 报表生成

### 系统工具
- 自动化脚本
- 监控系统
- 部署工具
- 测试框架

## 📈 最佳实践

### 任务描述
- 明确功能需求
- 指定技术栈
- 说明质量要求
- 提供使用场景

### 工作流选择
- 简单任务：使用基础工作流
- 复杂项目：使用高级工作流
- 需要架构设计：包含Architect
- 性能要求高：重点使用Optimizer

### 状态管理
- 长期项目：定期保存状态
- 中断恢复：使用状态加载
- 版本控制：保存多个状态版本

## 🔍 示例任务

运行完整示例：
```bash
python example_usage.py
```

包含的示例：
- 文件操作工具类
- LRU缓存实现
- 博客系统开发
- 数据分析系统
- 智能聊天机器人

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目！

## 📄 许可证

MIT License

## 🙏 致谢

- Microsoft AutoGen团队
- OpenAI API
- Python社区
