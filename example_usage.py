"""
AutoGen编程工作流使用示例
展示如何使用基础和高级工作流
"""

import asyncio
import os
from programming_workflow import ProgrammingWorkflow
from advanced_programming_workflow import AdvancedProgrammingWorkflow
from config import WorkflowConfig, TASK_TEMPLATES


async def basic_workflow_example():
    """基础工作流示例"""
    print("🔥 基础编程工作流示例")
    print("=" * 60)
    
    # 创建基础工作流
    workflow = ProgrammingWorkflow()
    
    try:
        # 示例1: 简单的工具类
        task1 = """
        请实现一个Python文件操作工具类，要求：
        1. 支持文件的读取、写入、复制、删除操作
        2. 支持目录的创建、删除、遍历
        3. 包含文件大小、修改时间等信息获取
        4. 提供批量操作功能
        5. 包含完整的错误处理
        """
        
        print("📝 任务: 文件操作工具类")
        await workflow.run_programming_task_stream(task1)
        
        print("\n" + "🔄" * 30 + "\n")
        
        # 示例2: 数据结构实现
        task2 = """
        请实现一个高效的LRU缓存类，要求：
        1. 支持get和put操作
        2. 当缓存满时自动淘汰最久未使用的项
        3. 时间复杂度O(1)
        4. 支持自定义缓存大小
        5. 包含统计信息（命中率、缓存大小等）
        """
        
        print("📝 任务: LRU缓存实现")
        await workflow.run_programming_task_stream(task2)
        
    finally:
        await workflow.close()


async def advanced_workflow_example():
    """高级工作流示例"""
    print("\n🚀 高级编程工作流示例")
    print("=" * 60)
    
    # 创建高级工作流
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        # 使用预定义模板
        web_app_task = TASK_TEMPLATES["web_application"] + """
        
        具体需求：
        - 开发一个博客系统
        - 支持用户注册、登录、发布文章
        - 包含文章分类和标签功能
        - 支持评论和点赞
        - 提供管理后台
        - 响应式设计，支持移动端
        """
        
        print("📝 任务: 博客系统开发")
        await workflow.run_advanced_task(web_app_task, "web_application")
        
        print("\n" + "🔄" * 30 + "\n")
        
        # 数据分析项目
        data_task = TASK_TEMPLATES["data_analysis"] + """
        
        具体需求：
        - 分析电商销售数据
        - 包含用户行为分析
        - 产品推荐算法
        - 销售趋势预测
        - 生成可视化报表
        - 支持实时数据处理
        """
        
        print("📝 任务: 电商数据分析系统")
        await workflow.run_advanced_task(data_task, "data_analysis")
        
    finally:
        await workflow.close()


async def state_management_example():
    """状态管理示例"""
    print("\n💾 状态管理示例")
    print("=" * 60)
    
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        # 开始一个任务
        task = "实现一个简单的计算器类，支持基本四则运算"
        
        print("📝 开始任务...")
        # 这里我们可以保存状态并在需要时恢复
        
        # 保存状态
        print("💾 保存工作流状态...")
        state = await workflow.save_team_state()
        
        print("✅ 状态已保存，可以在需要时恢复继续工作")
        
        # 在实际应用中，你可以：
        # 1. 将状态保存到文件或数据库
        # 2. 在程序重启后加载状态
        # 3. 继续之前的工作流
        
    finally:
        await workflow.close()


async def custom_task_example():
    """自定义任务示例"""
    print("\n🎯 自定义任务示例")
    print("=" * 60)
    
    workflow = AdvancedProgrammingWorkflow()
    
    try:
        # 自定义复杂任务
        custom_task = """
        设计并实现一个智能聊天机器人系统，要求：
        
        1. 核心功能：
           - 自然语言理解和生成
           - 多轮对话管理
           - 上下文记忆功能
           - 意图识别和实体提取
           
        2. 技术架构：
           - 微服务架构设计
           - 使用消息队列处理异步任务
           - 集成机器学习模型
           - 支持插件扩展
           
        3. 用户界面：
           - Web聊天界面
           - 移动端适配
           - 语音输入输出
           - 多媒体消息支持
           
        4. 运维要求：
           - 容器化部署
           - 监控和日志系统
           - 自动扩缩容
           - 数据备份和恢复
        """
        
        print("📝 任务: 智能聊天机器人系统")
        await workflow.run_advanced_task(custom_task, "ai_system")
        
    finally:
        await workflow.close()


async def main():
    """主函数"""
    # 验证配置
    if not WorkflowConfig.validate_config():
        print("❌ 配置验证失败，请检查环境变量设置")
        print("💡 请设置 OPENAI_API_KEY 环境变量")
        return
    
    print("🎉 AutoGen编程工作流示例")
    print("基于AutoGen最新版本的多Agent协作编程")
    print("=" * 80)
    
    # 创建输出目录
    os.makedirs(WorkflowConfig.TOOLS_CONFIG["output_directory"], exist_ok=True)
    
    try:
        # 运行不同的示例
        await basic_workflow_example()
        await advanced_workflow_example()
        await state_management_example()
        await custom_task_example()
        
        print("\n🎊 所有示例执行完成！")
        print("📁 生成的代码文件保存在 ./output 目录中")
        
    except Exception as e:
        print(f"❌ 执行过程中出现错误: {str(e)}")
        print("💡 请检查API密钥和网络连接")


if __name__ == "__main__":
    # 设置事件循环策略（Windows兼容性）
    if os.name == 'nt':
        asyncio.set_event_loop_policy(asyncio.WindowsProactorEventLoopPolicy())
    
    # 运行示例
    asyncio.run(main())
